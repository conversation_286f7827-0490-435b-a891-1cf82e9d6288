import makeWASocket, { type MessageUpsertType, type WAMessage, isJidBroadcast, isJidStatusBroadcast } from "baileys";
import { mastra } from "../mastra/index.js";
import { RuntimeContext } from "@mastra/core/di";
import type { ChatbotRuntimeContext } from "../mastra/agents/chatbot-agent.js";
import { formatMessageDateTime } from "../utils/messageParser.js";

export async function handleIncomingMessages(
    { messages, type }: { messages: WAMessage[]; type: MessageUpsertType },
    sock: ReturnType<typeof makeWASocket>
) {
    if (type !== "notify") return;

    for (const msg of messages) {
        if (!msg.message) continue;
        if (msg.key.fromMe) continue; // Ignore messages sent by the bot
        if (isJidBroadcast(msg.key.remoteJid!) || isJidStatusBroadcast(msg.key.remoteJid!)) continue;

        const messageText = getMessageText(msg);
        if (!messageText) continue;

        // Check if the bot should respond to this message (mentioned or replied to)
        if (!shouldBotRespond(msg, sock)) continue;

        const userJid = msg.key.participant || msg.key.remoteJid!;

        // Extract user ID and chat ID for proper memory management
        const userFormattedId = userJid.replace("@s.whatsapp.net", "").replace("@g.us", ""); // User who sent the message
        const chatJid = msg.key.remoteJid!; // Chat where message was sent

        console.log("Received message:", messageText, "from:", userFormattedId);

        // Extract sender information
        const senderName = await getSenderName(msg, sock);

        // Here you can integrate with your AI agent
        const response = await generateBotResponse(messageText, userJid, chatJid, msg, senderName);

        if (response) {
            await sock.sendMessage(msg.key.remoteJid!, { text: response });
        }
    }
}

function getMessageText(msg: WAMessage): string | null {
    if (msg.message?.conversation) {
        return msg.message.conversation;
    }
    if (msg.message?.extendedTextMessage?.text) {
        return msg.message.extendedTextMessage.text;
    }
    return null;
}

async function getSenderName(msg: WAMessage, _sock: ReturnType<typeof makeWASocket>): Promise<string> {
    try {
        const senderJid = msg.key.participant || msg.key.remoteJid!;

        // Try to get push name from message first (most reliable)
        if (msg.pushName) {
            return msg.pushName;
        }

        // Fallback to phone number (remove @s.whatsapp.net suffix)
        return senderJid.replace("@s.whatsapp.net", "").replace("@g.us", "");
    } catch (error) {
        console.warn("Error getting sender name:", error);
        // Fallback to JID without domain
        const senderJid = msg.key.participant || msg.key.remoteJid!;
        return senderJid.replace("@s.whatsapp.net", "").replace("@g.us", "");
    }
}

function shouldBotRespond(msg: WAMessage, sock: ReturnType<typeof makeWASocket>): boolean {
    const botJid = sock.user?.id;
    if (!botJid) {
        console.log("No bot JID available");
        return false;
    }

    // Debug: log all available bot identifiers
    console.log("Bot user info:", JSON.stringify(sock.user, null, 2));

    // Always respond to direct messages (non-group chats)
    if (!msg.key.remoteJid?.endsWith("@g.us")) {
        console.log("Direct message, responding");
        return true;
    }

    // For group chats, check if bot is mentioned or message is a reply to bot
    const isMentioned = isBotMentioned(msg, botJid, sock.user);
    const isReply = isReplyToBotMessage(msg, botJid);
    
    console.log(`Group message - Bot mentioned: ${isMentioned}, Is reply: ${isReply}, Bot JID: ${botJid}`);
    
    return isMentioned || isReply;
}

function isBotMentioned(msg: WAMessage, botJid: string, botUser?: any): boolean {
    // Check mentions in different message types
    let mentions: string[] = [];
    
    // Check extended text message mentions
    if (msg.message?.extendedTextMessage?.contextInfo?.mentionedJid) {
        mentions = mentions.concat(msg.message.extendedTextMessage.contextInfo.mentionedJid);
        console.log(`Found mentions in extendedTextMessage: ${msg.message.extendedTextMessage.contextInfo.mentionedJid.join(', ')}`);
    }
    
    console.log(`Checking mentions: [${mentions.join(', ')}] against bot JID: ${botJid}`);
    
    // Check both exact match and normalized match
    const exactMatch = mentions.includes(botJid);
    const normalizedBotJid = normalizeBotJid(botJid);
    
    const normalizedMatch = mentions.some(mention => {
        const normalizedMention = normalizeQuotedParticipant(mention);
        console.log(`Comparing normalized mention: ${normalizedMention} with bot: ${normalizedBotJid}`);
        return normalizedMention === normalizedBotJid;
    });
    
    // Check if any mention matches the bot's LID (if available)
    const lidMatch = botUser?.lid ? mentions.includes(`${botUser.lid}@lid`) : false;
    console.log(`LID check - Bot LID: ${botUser?.lid}, LID match: ${lidMatch}`);
    
    console.log(`Mention check - Exact: ${exactMatch}, Normalized: ${normalizedMatch}, LID: ${lidMatch}`);
    return exactMatch || normalizedMatch || lidMatch;
}

function isReplyToBotMessage(msg: WAMessage, botJid: string): boolean {
    // Check if the message is a reply to a bot message
    let quotedMessage = null;
    let quotedParticipant = null;
    
    // Check in extendedTextMessage
    if (msg.message?.extendedTextMessage?.contextInfo) {
        quotedMessage = msg.message.extendedTextMessage.contextInfo.quotedMessage;
        quotedParticipant = msg.message.extendedTextMessage.contextInfo.participant;
    }
    
    console.log(`Reply check - Quoted message exists: ${!!quotedMessage}, Quoted participant: ${quotedParticipant}, Bot JID: ${botJid}`);
    
    if (!quotedMessage || !quotedParticipant) {
        console.log("No quoted message or participant found");
        return false;
    }
    
    // Normalize JIDs for comparison - extract the phone number part
    const normalizedBotJid = normalizeBotJid(botJid);
    const normalizedQuotedParticipant = normalizeQuotedParticipant(quotedParticipant);
    
    console.log(`Normalized comparison - Bot: ${normalizedBotJid}, Quoted: ${normalizedQuotedParticipant}`);
    
    // Also check if the quoted participant matches the bot JID exactly (in case normalization is wrong)
    const exactMatch = quotedParticipant === botJid;
    console.log(`Exact JID match: ${exactMatch}`);
    
    // Check if the quoted message is from the bot
    const isFromBot = normalizedQuotedParticipant === normalizedBotJid || exactMatch;
    console.log(`Is reply from bot: ${isFromBot}`);
    
    return isFromBot;
}

function normalizeBotJid(botJid: string): string {
    // Extract phone number from bot JID (e.g., "447928792916:<EMAIL>" -> "447928792916")
    const parts = botJid.split(':');
    return parts[0]?.split('@')[0] || botJid;
}

function normalizeQuotedParticipant(participantJid: string): string {
    // Handle different formats like "@lid" suffix
    if (participantJid.includes('@lid')) {
        const parts = participantJid.split('@');
        return parts[0] || participantJid;
    }
    // Handle standard WhatsApp format
    const parts = participantJid.split(':');
    const firstPart = parts[0] || participantJid;
    const finalParts = firstPart.split('@');
    return finalParts[0] || firstPart;
}

async function generateBotResponse(
    messageText: string,
    userJid: string,
    chatJid: string,
    message: WAMessage,
    senderName: string
): Promise<string | null> {
    try {
        const agent = mastra.getAgent("chatbotAgent");
        const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
        runtimeContext.set("config_model", "gemini-2.5-flash");
        runtimeContext.set("config_botName", "Jeff");

        // Include current user information with the question and timestamp
        const timestamp = message.messageTimestamp ? Number(message.messageTimestamp) : Date.now() / 1000;
        const currentMessageDateTime = formatMessageDateTime(timestamp);
        const formattedMessage = `${senderName} [${currentMessageDateTime}]:\n${messageText}`;
        console.log("formattedMessage:", formattedMessage);

        const response = await agent.generate(formattedMessage, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
            runtimeContext,
        });

        return response.text || null;
    } catch (error) {
        console.error("Error generating bot response:", error);
        return null;
    }
}
