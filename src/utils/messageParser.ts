/**
 * Formats a timestamp into a readable date and time string
 * @param timestamp - Unix timestamp (in seconds or milliseconds)
 * @returns Formatted date and time string
 */
export const formatMessageDateTime = (timestamp: number): string => {
    try {
        // WhatsApp timestamps are typically in seconds, but sometimes in milliseconds
        // If timestamp is less than 1e12 (year 2001 in milliseconds), it's likely in seconds
        const date = new Date(timestamp < 1e12 ? timestamp * 1000 : timestamp);

        // Format as: "Dec 15, 2023 at 2:30 PM"
        const options: Intl.DateTimeFormatOptions = {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
        };

        return date.toLocaleString("en-US", options);
    } catch (error) {
        console.error("Error formatting timestamp:", error);
        return "Unknown time";
    }
};
