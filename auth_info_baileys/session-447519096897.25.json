{"_sessions": {"BbJPE0uvtfN9UVFSxULazkqSuJ4c0VyAYGCR/dPu27Bi": {"registrationId": *********, "currentRatchet": {"ephemeralKeyPair": {"pubKey": "BRRshnUPi0orhI+myjOp1Bg/dHZZ0A++6rl1fbTFpxgP", "privKey": "5+eM41NDT9goPHPetBDUICGUbAKN9FuRfWFBcACKo6k="}, "lastRemoteEphemeralKey": "Be/i3xkWtxbjolZ1fdN6P0KfU3Koww0RQFe7lGP21wx+", "previousCounter": 0, "rootKey": "s2iwZnxKNByX7/7W7M/lKDks0m04El/jnZHUnwt0YIs="}, "indexInfo": {"baseKey": "BbJPE0uvtfN9UVFSxULazkqSuJ4c0VyAYGCR/dPu27Bi", "baseKeyType": 2, "closed": 1753990217508, "used": 1753989889849, "created": 1753986862255, "remoteIdentityKey": "BRoq0mYSMMvulDg6e5mkerA6yd9W7px1XQrHsPQDhdBy"}, "_chains": {"BSXqao7XFa21bq1cxo4P8PE54ICPU7pMnTiNE3Z1jit4": {"chainKey": {"counter": 1}, "chainType": 2, "messageKeys": {}}, "BS5BFaqyZH02qNJD3lXHieXJPaOLswNgv8jrburXLaxY": {"chainKey": {"counter": 1}, "chainType": 2, "messageKeys": {}}, "BdzN6nnKG58jSOBtlBo1y9E8CsS0G0s7WVSxu+Ip9YM8": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BTJhB2xPZRTPSK7UFaZ3KXJd3zOzEzxqHYfBLBq6Gct/": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BYdJo7VTfK53+cvFQ/eMc0aA0Xl2Egs4egleCykKaxAa": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BTrYQlk3A41MnVcHVK5XYX+kNmMjXTYjKygxHxMd1ypf": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BTzBY+cszgPcJkShEo/TPCSNNKYUPLbqKS4zrpKyiTs1": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BbUQId+D13dRUyU85y569F8Ej8tzF3npuvNCiIOOGucE": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BRcjrc4CDmX7Pg8QcVW/u57+5AvqIaHFKCq9xhcW2lF3": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BSbTQWZQp4rQJr5D9Ep7vN1I9mBVmQNIQ5VVoAN2DLFK": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BXmg6fQrXvG6ufQqibV8vW4LT2MbP7GR1Mqai25Vncp1": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BR0NQac759DhCIJEFdF4SIROMsc1jCKYQ4uiZDe2pHtG": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "Bb0f4azoz1MhO9f0eBr8E1N9aBDwDrkHJvrHuEaVuUMX": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BdPn2vWfxZJf6rRGxOQUXXZLgnK+xJwOgqNoQSLMIRJb": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "Be/i3xkWtxbjolZ1fdN6P0KfU3Koww0RQFe7lGP21wx+": {"chainKey": {"counter": 0, "key": "TDpmCXKFIYF2XCbwM3yVn2WjsoY+lqKnSlcaOtTxzpg="}, "chainType": 2, "messageKeys": {}}, "BRRshnUPi0orhI+myjOp1Bg/dHZZ0A++6rl1fbTFpxgP": {"chainKey": {"counter": 0, "key": "DHXWn6Y/a5SZS4AGG8+sXyqVmjfLCVBogHdO11hxMnM="}, "chainType": 1, "messageKeys": {}}}}, "BUlHb0dJKdq4r9DX/iP8vy6Of/j883IHK1vAfEEPbyhc": {"registrationId": *********, "currentRatchet": {"ephemeralKeyPair": {"pubKey": "Bfc3ZAzR3Xqr7emzJpjXtVCb6q59n+7nGYoV9jgfgU81", "privKey": "P2qM8Z9kcZBrK3QEY0qCQtZWWxQCvJ4DbrqF/XO0j7I="}, "lastRemoteEphemeralKey": "BZtzZZjG2Fsp/1Yh2K4NgH1Xs9KJPC+dh9jZm7EwU8Mo", "previousCounter": 0, "rootKey": "oEVERK228XlQ4hGiiZ5wkqCDfEh0XuZwJRV+C4mB3LM="}, "indexInfo": {"baseKey": "BUlHb0dJKdq4r9DX/iP8vy6Of/j883IHK1vAfEEPbyhc", "baseKeyType": 2, "closed": -1, "used": 1753990234541, "created": 1753990217509, "remoteIdentityKey": "BRoq0mYSMMvulDg6e5mkerA6yd9W7px1XQrHsPQDhdBy"}, "_chains": {"BZTzHipRNvtda7kwK6EhqAX3ipTU/HmatRMVWzk2z7h+": {"chainKey": {"counter": 0}, "chainType": 2, "messageKeys": {}}, "BZtzZZjG2Fsp/1Yh2K4NgH1Xs9KJPC+dh9jZm7EwU8Mo": {"chainKey": {"counter": 0, "key": "BdRwr72xqmtfBROynIjnnITZaQPGkg8O6UmW2N8bf5M="}, "chainType": 2, "messageKeys": {}}, "Bfc3ZAzR3Xqr7emzJpjXtVCb6q59n+7nGYoV9jgfgU81": {"chainKey": {"counter": 0, "key": "vL680gXta7Re1geRikXI3PO7b68uZhkzzJ0InMuDf/I="}, "chainType": 1, "messageKeys": {}}}}}, "version": "v1"}