{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "L683I6HkqC1eqA/elH/96KVihUoWfGjfQSZSXe7atbg="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "3z/OlVuwqqRnZ3piXJ27yWNyGZtIuXIB+bbqSUikng8="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "x3RTFBZyb3P7sWJqUc5xmU9S6yW19Pmi4oQMlBmiGZU="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "fiIE5YNgaBo5epMFnfTOnWOzAdD2uRGEiBCDivdIn0w="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "X7Z/xtdm63GkWyLN8h6FSUHe2GikLpWJpdUgGXUvIJw="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "0GFn87b3cMFsbJKMeZwlkT9BH9bcjVBVae/Gl3Sy/k8="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "d3AMmvCNvm07srEv3XHGS4lvcoqQvFP4+EJYfKQa06c="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "2OQIRa4mRjHfsiFw6aeaVUFczb1/39KUF2DptPgQRXI="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "z8Uc6FC+6T3ASepR/tztDBBe9zcmKbAlRwK3ASP8ipB80Hi1mrHCy55OejXTR4wcUvOz6XGUtKZPdEOOXo1NCg=="}, "keyId": 1}, "registrationId": 152, "advSecretKey": "Z1XKre6fzSHRiXffvQqzWulMX8YOtaia1BhVuAqZwcI=", "processedHistoryMessages": [{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "3288DF9724A02B70294CF19CDCC12C85"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "C57CFA86EC9AEDC614537E2B0283A3AF"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "0952F8F9CB870BC04E6E76DBA24F5473"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "E141F89E0FDD2C89BFF8D87BAD7C9004"}, "messageTimestamp": **********}], "nextPreKeyId": 37, "firstUnuploadedPreKeyId": 37, "accountSyncCounter": 1, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CKHc64kDEJvtrsQGGAMgACgA", "accountSignatureKey": "GJPF/yBrThKKGRUXqT1KYXHvVk/EifT75HCof8hZ8lE=", "accountSignature": "k8lyhPcc4j0SCmnPMq2+nxasYtcnxlyVq0uyZa6mLMVAs+KShULTYY2xyKUuvD5eFeuwyuWYhwF0qEaslcZwAw==", "deviceSignature": "A5wHAQqhnV0u75aqYjaiYEJc98xShF+Mc6Wa9L7DgCABLaSntw5OSdiABZQjnC/nEe0Sc5fJra7TxBqkgpBAAg=="}, "me": {"id": "************:<EMAIL>", "lid": "***************:9@lid", "name": "Helpful and Harmless AI 2"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BRiTxf8ga04SihkVF6k9SmFx71ZPxIn0++RwqH/IWfJR"}}], "platform": "android", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CAsIEg=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "1K4hH4", "myAppStateKeyId": "AAAAAIY+"}